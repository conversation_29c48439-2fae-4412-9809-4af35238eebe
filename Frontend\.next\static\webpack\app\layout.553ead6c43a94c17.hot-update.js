/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CBootstrapClient%5CBootstrapClient.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCircularButtonWithArrow%5CCircularButtonWithArrow.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCookiesConsentBanner%5CCookiesConsentBanner.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CFooter%5CFooter.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeader%5CHeader.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbase.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CBootstrapClient%5CBootstrapClient.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCircularButtonWithArrow%5CCircularButtonWithArrow.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCookiesConsentBanner%5CCookiesConsentBanner.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CFooter%5CFooter.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeader%5CHeader.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbase.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/BootstrapClient/BootstrapClient.tsx */ \"(app-pages-browser)/./src/components/BootstrapClient/BootstrapClient.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CircularButtonWithArrow/CircularButtonWithArrow.tsx */ \"(app-pages-browser)/./src/components/CircularButtonWithArrow/CircularButtonWithArrow.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CookiesConsentBanner/CookiesConsentBanner.tsx */ \"(app-pages-browser)/./src/components/CookiesConsentBanner/CookiesConsentBanner.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer/Footer.tsx */ \"(app-pages-browser)/./src/components/Footer/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header/Header.tsx */ \"(app-pages-browser)/./src/components/Header/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/bootstrap/dist/css/bootstrap.css */ \"(app-pages-browser)/../../node_modules/bootstrap/dist/css/bootstrap.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/base.css */ \"(app-pages-browser)/./src/styles/base.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/script.js */ \"(app-pages-browser)/../../node_modules/next/dist/client/script.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js */ \"(app-pages-browser)/../../node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(app-pages-browser)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"100\\\",\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CBootstrapClient%5CBootstrapClient.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCircularButtonWithArrow%5CCircularButtonWithArrow.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCookiesConsentBanner%5CCookiesConsentBanner.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CFooter%5CFooter.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeader%5CHeader.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbase.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&server=false!\n"));

/***/ })

});